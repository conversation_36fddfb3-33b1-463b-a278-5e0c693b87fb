<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.sc.mapper.ScCommunityMapper">
    
    <resultMap type="com.ruoyi.sc.domain.ScCommunity" id="ScCommunityResult">
        <result property="id" column="id"/>
        <result property="uploadCommunity" column="upload_Community"/>
    </resultMap>

    <sql id="selectScCommunityVo">
        select id, upload_Community from sc_community
    </sql>

    <select id="selectScCommunityList" parameterType="com.ruoyi.sc.domain.ScCommunity" resultMap="ScCommunityResult">
        <include refid="selectScCommunityVo"/>
        <where>  
            <if test="uploadCommunity != null  and uploadCommunity != ''"> and upload_Community like concat('%', #{uploadCommunity}, '%')</if>
        </where>
    </select>
    
    <select id="selectScCommunityById" parameterType="Long" resultMap="ScCommunityResult">
        <include refid="selectScCommunityVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertScCommunity" parameterType="com.ruoyi.sc.domain.ScCommunity" useGeneratedKeys="true" keyProperty="id">
        insert into sc_community
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uploadCommunity != null">upload_Community,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uploadCommunity != null">#{uploadCommunity},</if>
         </trim>
    </insert>

    <update id="updateScCommunity" parameterType="com.ruoyi.sc.domain.ScCommunity">
        update sc_community
        <trim prefix="SET" suffixOverrides=",">
            <if test="uploadCommunity != null">upload_Community = #{uploadCommunity},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScCommunityById" parameterType="Long">
        delete from sc_community where id = #{id}
    </delete>

    <delete id="deleteScCommunityByIds" parameterType="String">
        delete from sc_community where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>