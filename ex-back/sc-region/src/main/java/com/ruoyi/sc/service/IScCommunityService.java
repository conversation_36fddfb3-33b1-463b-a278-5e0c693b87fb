package com.ruoyi.sc.service;

import java.util.List;
import com.ruoyi.sc.domain.ScCommunity;

/**
 * 小区信息Service接口
 * 
 * <AUTHOR>
 */
public interface IScCommunityService 
{
    /**
     * 查询小区信息
     * 
     * @param id 小区信息主键
     * @return 小区信息
     */
    public ScCommunity selectScCommunityById(Long id);

    /**
     * 查询小区信息列表
     * 
     * @param scCommunity 小区信息
     * @return 小区信息集合
     */
    public List<ScCommunity> selectScCommunityList(ScCommunity scCommunity);

    /**
     * 新增小区信息
     * 
     * @param scCommunity 小区信息
     * @return 结果
     */
    public int insertScCommunity(ScCommunity scCommunity);

    /**
     * 修改小区信息
     * 
     * @param scCommunity 小区信息
     * @return 结果
     */
    public int updateScCommunity(ScCommunity scCommunity);

    /**
     * 批量删除小区信息
     * 
     * @param ids 需要删除的小区信息主键集合
     * @return 结果
     */
    public int deleteScCommunityByIds(String ids);

    /**
     * 删除小区信息信息
     * 
     * @param id 小区信息主键
     * @return 结果
     */
    public int deleteScCommunityById(Long id);
}