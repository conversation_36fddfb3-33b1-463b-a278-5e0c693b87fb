package com.ruoyi.sc.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sc.domain.ScCommunity;
import com.ruoyi.sc.service.IScCommunityService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区信息Controller
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/sc/community")
public class ScCommunityController extends BaseController
{
    private String prefix = "sc/community";

    @Autowired
    private IScCommunityService scCommunityService;

    @RequiresPermissions("sc:community:view")
    @GetMapping()
    public String community()
    {
        return prefix + "/community";
    }

    /**
     * 查询小区信息列表
     */
    @RequiresPermissions("sc:community:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ScCommunity scCommunity)
    {
        startPage();
        List<ScCommunity> list = scCommunityService.selectScCommunityList(scCommunity);
        return getDataTable(list);
    }

    /**
     * 导出小区信息列表
     */
    @RequiresPermissions("sc:community:export")
    @Log(title = "小区信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ScCommunity scCommunity)
    {
        List<ScCommunity> list = scCommunityService.selectScCommunityList(scCommunity);
        ExcelUtil<ScCommunity> util = new ExcelUtil<ScCommunity>(ScCommunity.class);
        return util.exportExcel(list, "小区信息数据");
    }

    /**
     * 新增小区信息
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存小区信息
     */
    @RequiresPermissions("sc:community:add")
    @Log(title = "小区信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ScCommunity scCommunity)
    {
        return toAjax(scCommunityService.insertScCommunity(scCommunity));
    }

    /**
     * 修改小区信息
     */
    @RequiresPermissions("sc:community:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        ScCommunity scCommunity = scCommunityService.selectScCommunityById(id);
        mmap.put("scCommunity", scCommunity);
        return prefix + "/edit";
    }

    /**
     * 修改保存小区信息
     */
    @RequiresPermissions("sc:community:edit")
    @Log(title = "小区信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ScCommunity scCommunity)
    {
        return toAjax(scCommunityService.updateScCommunity(scCommunity));
    }

    /**
     * 删除小区信息
     */
    @RequiresPermissions("sc:community:remove")
    @Log(title = "小区信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(scCommunityService.deleteScCommunityByIds(ids));
    }
}