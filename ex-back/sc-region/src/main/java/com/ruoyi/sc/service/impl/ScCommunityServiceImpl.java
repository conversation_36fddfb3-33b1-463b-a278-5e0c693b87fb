package com.ruoyi.sc.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.sc.mapper.ScCommunityMapper;
import com.ruoyi.sc.domain.ScCommunity;
import com.ruoyi.sc.service.IScCommunityService;
import com.ruoyi.common.core.text.Convert;

/**
 * 小区信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ScCommunityServiceImpl implements IScCommunityService 
{
    @Autowired
    private ScCommunityMapper scCommunityMapper;

    /**
     * 查询小区信息
     * 
     * @param id 小区信息主键
     * @return 小区信息
     */
    @Override
    public ScCommunity selectScCommunityById(Long id)
    {
        return scCommunityMapper.selectScCommunityById(id);
    }

    /**
     * 查询小区信息列表
     * 
     * @param scCommunity 小区信息
     * @return 小区信息
     */
    @Override
    public List<ScCommunity> selectScCommunityList(ScCommunity scCommunity)
    {
        return scCommunityMapper.selectScCommunityList(scCommunity);
    }

    /**
     * 新增小区信息
     * 
     * @param scCommunity 小区信息
     * @return 结果
     */
    @Override
    public int insertScCommunity(ScCommunity scCommunity)
    {
        return scCommunityMapper.insertScCommunity(scCommunity);
    }

    /**
     * 修改小区信息
     * 
     * @param scCommunity 小区信息
     * @return 结果
     */
    @Override
    public int updateScCommunity(ScCommunity scCommunity)
    {
        return scCommunityMapper.updateScCommunity(scCommunity);
    }

    /**
     * 批量删除小区信息
     * 
     * @param ids 需要删除的小区信息主键
     * @return 结果
     */
    @Override
    public int deleteScCommunityByIds(String ids)
    {
        return scCommunityMapper.deleteScCommunityByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除小区信息信息
     * 
     * @param id 小区信息主键
     * @return 结果
     */
    @Override
    public int deleteScCommunityById(Long id)
    {
        return scCommunityMapper.deleteScCommunityById(id);
    }
}