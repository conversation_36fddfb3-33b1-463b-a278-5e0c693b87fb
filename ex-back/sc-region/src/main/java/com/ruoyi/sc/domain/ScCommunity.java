package com.ruoyi.sc.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 小区信息对象 sc_community
 * 
 * <AUTHOR>
 */
public class ScCommunity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 上传小区 */
    @Excel(name = "上传小区")
    private String uploadCommunity;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUploadCommunity(String uploadCommunity) 
    {
        this.uploadCommunity = uploadCommunity;
    }

    public String getUploadCommunity() 
    {
        return uploadCommunity;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uploadCommunity", getUploadCommunity())
            .toString();
    }
}